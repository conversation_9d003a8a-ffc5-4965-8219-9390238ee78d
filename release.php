<?php include_once '../lib/config.php';
user();
$uid = $_SESSION['userid'];

if (isset($_GET['recid']) && $_GET['recid']) {
    $recid = (int) $_GET['recid'];

    // Validate recid
    if($recid <= 0) {
        setMessage('Invalid investment record.', 'error');
        redirect('./report_invest.php');
        exit;
    }

    $datetime_120 = date('Y-m-d H:i:s', strtotime('-90 days', strtotime(date('c'))));
    $check = my_fetch_object(my_query("SELECT * FROM investments WHERE recid='".$recid."' AND uid='".$uid."' AND status = 0 AND amount2 > 0 AND datetime <= '" . $datetime_120 . "'"));
    
    if ($check) {
        $time_floor = floor((strtotime(date('c')) - strtotime($check->datetime)) / (60 * 60 * 24));
        $amt = 0;
        
        /*if($time_floor >= 690){
            $amt = $check->amount2;
        }
        elseif($time_floor >= 660){
            $amt = $check->amount2-$check->amount*0.05;
        }
        elseif($time_floor >= 630){
            $amt = $check->amount2-$check->amount*0.10;
        }
        elseif($time_floor >= 600){
            $amt = $check->amount2-$check->amount*0.15;
        }
        elseif($time_floor >= 570){
            $amt = $check->amount2-$check->amount*0.20;
        }
        elseif($time_floor >= 540){
            $amt = $check->amount2-$check->amount*0.25;
        }
        elseif($time_floor >= 510){
            $amt = $check->amount2-$check->amount*0.30;
        }
        elseif($time_floor >= 480){
            $amt = $check->amount2-$check->amount*0.35;
        }
        elseif($time_floor >= 450){
            $amt = $check->amount2-$check->amount*0.40;
        }
        elseif($time_floor >= 420){
            $amt = $check->amount2-$check->amount*0.45;
        }
        elseif($time_floor >= 390){
            $amt = $check->amount2-$check->amount*0.50;
        }
        elseif($time_floor >= 360){
            $amt = $check->amount2-$check->amount*0.55;
        }
        elseif($time_floor >= 330){
            $amt = $check->amount2-$check->amount*0.60;
        }
        elseif($time_floor >= 300){
            $amt = $check->amount2-$check->amount*0.65;
        }
        elseif($time_floor >= 270){
            $amt = $check->amount2-$check->amount*0.70;
        }
        elseif($time_floor >= 240){
            $amt = $check->amount2-$check->amount*0.75;
        }
        elseif($time_floor >= 210){
            $amt = $check->amount2-$check->amount*0.80;
        }
        else*/ if($time_floor >= 180){
            $amt = $check->amount2*0.90;
        }
        elseif($time_floor >= 90){
            $amt = $check->amount2*0.80;
        }
        else{
            setMessage('Release after 90 days.', 'error');
        }
        
        if($amt > 0){
            // Start transaction-like operations
            $success = true;

            // Update user wallet - add the released amount
            $wallet_update = my_query("UPDATE user SET wallet=wallet+'" . $amt . "' WHERE uid='" . $uid . "'");
            if(!$wallet_update) {
                $success = false;
            }

            // Update investment - decrease amount2 by released amount and set release datetime
            $new_amount2 = $check->amount2 - $amt;

            // If this is a full release (amount2 becomes 0 or negative), set status to 1 (completed)
            if($new_amount2 <= 0) {
                $investment_update = my_query("UPDATE investments SET amount2 = 0, rdatetime = '" . date('c') . "', status = 1 WHERE recid='" . $recid . "'");
            } else {
                // Partial release - keep status 0 but update amount2 and rdatetime
                $investment_update = my_query("UPDATE investments SET amount2 = '" . $new_amount2 . "', rdatetime = '" . date('c') . "' WHERE recid='" . $recid . "'");
            }

            if(!$investment_update) {
                $success = false;
            }

            if($success) {
                setMessage('ROI of ₹' . number_format($amt, 2) . ' released successfully to your wallet.', 'success');
            } else {
                setMessage('Error occurred during release. Please try again.', 'error');
            }
        }
    } else {
        // No valid investment found for release
        setMessage('No eligible investment found for release. Investment must be at least 90 days old and have remaining balance.', 'error');
    }
} else {
    // No recid provided
    setMessage('Invalid request. Investment record not specified.', 'error');
}
redirect('./report_invest.php');
?>