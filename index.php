<?php
$type = (isset($_GET['type']) && (int) $_GET['type'] <= 6) ? (int) $_GET['type'] : 0;
$title = ($type == 2) ? "Super Jackpot Income" : (($type == 1) ? "Stacking Investments" : "All Trades");
//$title = "Investments";
include_once 'header.php';

// Get investments data
$query = "SELECT i.*, ip.title FROM investments as i"
        . " LEFT JOIN investments_plan as ip ON ip.recid=i.ipid"
        . " WHERE i.uid='".$uid."'";

// $query .= ($type == 1) ? " AND i.ipid >= 6" : " AND i.ipid <= 5";
$query .= " ORDER BY i.datetime DESC";
$result = my_query($query);

// Calculate summary statistics
$total_invested = 0;
$total_remaining = 0;
$total_tokens = 0;
$total_investments = 0;

$temp_result = my_query($query);
while ($row = mysqli_fetch_object($temp_result)) {
    $total_invested += $row->amount;
    $total_remaining += $row->amount2;
    $total_tokens += $row->bonus;
    $total_investments++;
}

// Reset counter
$i=0;
?>

<style>
    body, #page-wrapper {
        background-color: #0b0e11;
    }
    .content-header {
        display: none;
    }

    .investments-wrapper {
        padding: 15px;
        color: #eaecef;
        margin: 0 auto;
        /*max-width: 1200px;*/
    }

    /* Investments Header */
    .investments-header {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        padding: 25px 30px;
        margin-bottom: 25px;
        position: relative;
        overflow: hidden;
        animation: slideInFade 0.8s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .investments-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .investments-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100px;
        background: radial-gradient(ellipse at bottom, rgba(240, 185, 11, 0.1), transparent 70%);
        pointer-events: none;
    }

    .investments-title {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .investments-title h2 {
        font-size: 24px;
        color: #fff;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .investments-title h2 i {
        margin-right: 12px;
        color: #f0b90b;
    }

    .investments-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 15px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
        z-index: -1;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
    }

    .stat-label {
        font-size: 14px;
        color: #848e9c;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .stat-label i {
        margin-right: 8px;
        color: #f0b90b;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
        display: flex;
        align-items: baseline;
    }

    .stat-value.earnings {
        color: #0ecb81;
    }

    .stat-value .currency {
        font-size: 14px;
        margin-right: 4px;
        opacity: 0.7;
    }

    .stat-value .unit {
        font-size: 14px;
        margin-left: 4px;
        opacity: 0.7;
    }

    /* Investments Card */
    .investments-card {
        background: linear-gradient(135deg, #1c2127 0%, #121517 100%);
        border-radius: 12px;
        margin-bottom: 25px;
        overflow: hidden;
        animation: slideUp 0.6s ease-out;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.05);
        position: relative;
    }

    .investments-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, #f0b90b, transparent);
    }

    .card-header {
        background: rgba(0, 0, 0, 0.2);
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header h3 {
        margin: 0;
        font-size: 16px;
        color: #f0b90b;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .card-header h3 i {
        margin-right: 10px;
    }

    .card-header-actions {
        display: flex;
        gap: 10px;
    }

    .card-filter {
        background: rgba(255, 255, 255, 0.05);
        border: none;
        color: #eaecef;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .card-filter:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 0;
    }

    /* Table Styling */
    .investments-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .investments-table th {
        background: rgba(0, 0, 0, 0.2);
        color: #848e9c;
        font-size: 12px;
        font-weight: 500;
        text-transform: uppercase;
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .investments-table td {
        padding: 15px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        color: #eaecef;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .investments-table tr:last-child td {
        border-bottom: none;
    }

    .investments-table tr:hover td {
        background: rgba(255, 255, 255, 0.03);
    }

    /* Plan Badge */
    .plan-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(240, 185, 11, 0.1);
        color: #f0b90b;
        border: 1px solid rgba(240, 185, 11, 0.2);
    }

    /* Amount Values */
    .amount-value {
        font-weight: 600;
        color: #0ecb81;
    }

    .remaining-value {
        font-weight: 600;
        color: #f0b90b;
    }

    .token-value {
        font-weight: 600;
        color: #1da1f2;
    }

    /* Action Button */
    .btn-release {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        background: linear-gradient(90deg, #f0b90b, #f8d33a);
        color: #0b0e11;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-release:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .btn-release i {
        margin-right: 5px;
    }

    /* Empty State */
    .empty-state {
        padding: 40px 20px;
        text-align: center;
        color: #848e9c;
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        color: rgba(240, 185, 11, 0.3);
    }

    .empty-text {
        font-size: 16px;
        margin-bottom: 15px;
    }

    .empty-subtext {
        font-size: 14px;
        opacity: 0.7;
        max-width: 400px;
        margin: 0 auto;
    }

    /* Date Display */
    .date-display {
        display: flex;
        flex-direction: column;
    }

    .date-primary {
        font-weight: 500;
        color: #eaecef;
    }

    .date-secondary {
        font-size: 12px;
        color: #848e9c;
        margin-top: 3px;
    }

    /* Responsive Adjustments */
    @media (max-width: 992px) {
        .investments-stats {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .investments-header {
            padding: 20px;
        }

        .investments-table {
            display: block;
            overflow-x: auto;
        }

        .investments-stats {
            grid-template-columns: 1fr;
        }

        .card-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .card-header-actions {
            width: 100%;
            justify-content: space-between;
        }
    }

    /* Animations */
    @keyframes slideInFade {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes slideUp {
        from {
            transform: translateY(20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>
<div class="investments-wrapper">
    <!-- Investments Header with Stats -->
    <div class="investments-header">
        <div class="investments-title">
            <h2><i class="fas fa-chart-line"></i> <?php echo $title; ?></h2>
        </div>
        <div class="investments-stats">
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-wallet"></i> Total Invested</div>
                <div class="stat-value earnings"><span class="currency"></span><?php echo number_format($total_invested, 2); ?> </div>
            </div>
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-coins"></i> Remaining Amount</div>
                <div class="stat-value"><span class="currency"></span><?php echo number_format($total_remaining, 2); ?> </div>
            </div>
           
            <div class="stat-card">
                <div class="stat-label"><i class="fas fa-file-invoice-dollar"></i> Investments</div>
                <div class="stat-value"><?php echo $total_investments; ?></div>
            </div>
        </div>
    </div>

    <!-- Investments List Card -->
    <div class="investments-card">
        <div class="card-header">
            <h3><i class="fas fa-list"></i> Investment History</h3>
            <div class="card-header-actions">
                <a href="?type=0" class="card-filter <?php echo $type == 0 ? 'active' : ''; ?>">All Investments</a>
            </div>
        </div>
        <div class="card-body">
            <table class="investments-table">
                <thead>
                    <tr>
                        <th width="50">#</th>
                        <th>Date</th>
                        <th>Amount</th>
                        <th>Remaining</th>
                        <th>Earned ROI</th>
                        <?php /*<th>Booster</th>
                        <th>Booster Direct Amount</th>*/?>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if (mysqli_num_rows($result) > 0) {
                        while ($row = mysqli_fetch_object($result)){$i++;?>
                        <tr>
                            <td><?php echo $i;?></td>
                            <!--<td><span class="plan-badge"><?php echo $row->title;?></span></td>-->
                            <td>
                                <div class="date-display">
                                    <span class="date-primary"><?php echo date("d M, Y", strtotime($row->datetime));?></span>
                                    <span class="date-secondary"><?php echo date("h:i A", strtotime($row->datetime));?></span>
                                </div>
                            </td>
                            <td><span class="amount-value"><?php echo number_format($row->amount*1, 2);?>  </span></td>
                            <td><span class="remaining-value"><?php echo number_format($row->amount2*1, 2);?>  </span></td>
                             <td><span class="Earned-Roi"><?php echo number_format($row->total_roi*1, 2);?>  </span></td>
                            <?php /*<td><?php echo $row->amount_booster*1;?></td>
                            <td><?php echo $row->amount_booster_direct*1;?></td>*/?>
                            <!--<td><span class="token-value"><?php echo number_format($row->bonus*1, 2);?></span></td>-->
                            <td>
                                <?php if($row->status == 0 && $row->amount !=0 && $row->amount2!=0){?>
                                    <a href="release.php?recid=<?php echo $row->recid;?>" onclick="return confirm('Are you sure?');" class="btn-release">
                                        <i class="fas fa-unlock"></i> Release
                                    </a>
                                <?php } else { ?>
                                    <span class="not-available">-</span>
                                <?php } ?>
                            </td>
                        </tr>
                        <?php }
                    } else { ?>
                        <tr>
                            <td colspan="7">
                                <div class="empty-state">
                                    <div class="empty-icon"><i class="fas fa-file-invoice-dollar"></i></div>
                                    <div class="empty-text">No investments found</div>
                                    <div class="empty-subtext">You don't have any investments in this category yet</div>
                                </div>
                            </td>
                        </tr>
                    <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php include_once 'footer.php'; ?>